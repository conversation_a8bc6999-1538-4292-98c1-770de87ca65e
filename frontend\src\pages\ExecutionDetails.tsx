import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom'
import { FlowExecution } from '@rpa-project/shared'
import { executionApi } from '../services/api'

export function ExecutionDetails() {
  const { id } = useParams<{ id: string }>()
  const [execution, setExecution] = useState<FlowExecution | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isResultsExpanded, setIsResultsExpanded] = useState(false)

  useEffect(() => {
    if (id) {
      loadExecution()
      
      // Auto-refresh if execution is still running
      const interval = setInterval(() => {
        if (execution?.status === 'running' || execution?.status === 'pending') {
          loadExecution()
        }
      }, 2000)
      
      return () => clearInterval(interval)
    }
  }, [id, execution?.status])

  const loadExecution = async () => {
    if (!id) return

    try {
      const response = await executionApi.getExecution(id)
      setExecution(response.data || null)
      setError(null)
    } catch (err) {
      setError('Misslyckades att ladda körningsdetaljer')
      console.error('Error loading execution:', err)
    } finally {
      setLoading(false)
    }
  }

  const getLogLevelStyles = (level: string) => {
    switch (level) {
      case 'error':
        return {
          badge: 'log-badge-error',
          icon: '❌'
        }
      case 'warn':
        return {
          badge: 'log-badge-warn',
          icon: '⚠️'
        }
      case 'info':
        return {
          badge: 'log-badge-info',
          icon: 'ℹ️'
        }
      case 'debug':
        return {
          badge: 'log-badge-debug',
          icon: '🔍'
        }
      default:
        return {
          badge: 'log-badge-info',
          icon: '📝'
        }
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Slutförd'
      case 'running':
        return 'Körs'
      case 'pending':
        return 'Väntar'
      case 'failed':
        return 'Misslyckad'
      case 'cancelled':
        return 'Avbruten'
      default:
        return status
    }
  }

  const getStatusButtonClass = (status: string) => {
    const baseClass = "status-button"

    switch (status) {
      case 'completed':
        return `${baseClass} status-completed`
      case 'running':
        return `${baseClass} status-running`
      case 'pending':
        return `${baseClass} status-scheduled`
      case 'failed':
        return `${baseClass} status-failed`
      case 'cancelled':
        return `${baseClass} status-failed`
      default:
        return `${baseClass} status-scheduled`
    }
  }

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-text">Laddar körningsdetaljer...</div>
      </div>
    )
  }

  if (error || !execution) {
    return (
      <div className="dashboard-container">
        <div className="error-card">
          <h3 className="error-title">Fel vid laddning</h3>
          <p className="error-message">{error || 'Körning hittades inte'}</p>
        </div>
        <div style={{ padding: '1rem' }}>
          <Link to="/executions" className="action-button secondary">
            <span>← Tillbaka till körningar</span>
          </Link>
        </div>
      </div>
    )
  }

  const formatDuration = (start: Date, end?: Date) => {
    const startTime = new Date(start).getTime()
    const endTime = end ? new Date(end).getTime() : Date.now()
    const duration = endTime - startTime
    
    if (duration < 1000) return `${duration}ms`
    if (duration < 60000) return `${Math.round(duration / 1000)}s`
    return `${Math.round(duration / 60000)}m ${Math.round((duration % 60000) / 1000)}s`
  }

  return (
    <div className="dashboard-container">
      {/* Header */}
      <div className="dashboard-header">
        <div className="dashboard-header-content">
          <p className="dashboard-title">Körningsdetaljer</p>
          <p className="dashboard-subtitle">
            Detaljerad information om körning {execution.id.slice(0, 8)}...
          </p>
        </div>
        <Link to="/executions" className="action-button secondary">
          <span>← Tillbaka till körningar</span>
        </Link>
      </div>

      {/* Stats Cards */}
      <div className="stats-grid">
        <div className="stat-card">
          <p className="stat-label">Status</p>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
            <button className={getStatusButtonClass(execution.status)}>
              <span>{getStatusText(execution.status)}</span>
            </button>
          </div>
        </div>
        <div className="stat-card">
          <p className="stat-label">Startad</p>
          <p className="stat-value">
            {new Date(execution.startedAt).toLocaleString('sv-SE', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit'
            })}
          </p>
        </div>
        <div className="stat-card">
          <p className="stat-label">Varaktighet</p>
          <p className="stat-value">{formatDuration(execution.startedAt, execution.completedAt)}</p>
        </div>
        <div className="stat-card">
          <p className="stat-label">Loggposter</p>
          <p className="stat-value">{execution.logs.length}</p>
        </div>
      </div>

      {/* Error Display */}
      {execution.error && (
        <div className="error-card">
          <h3 className="error-title">Feldetaljer</h3>
          <p className="error-message">{execution.error}</p>
        </div>
      )}

      {/* Results */}
      {execution.results && Object.keys(execution.results).length > 0 && (
        <>
          <div
            onClick={() => setIsResultsExpanded(!isResultsExpanded)}
            style={{
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              padding: '1rem 1rem 0.75rem 1rem',
              margin: 0,
              userSelect: 'none'
            }}
          >
            <h2 className="section-title" style={{ margin: 0, padding: 0 }}>
              Resultat
            </h2>
            <span style={{
              transform: isResultsExpanded ? 'rotate(180deg)' : 'rotate(0deg)',
              transition: 'transform 0.2s ease',
              fontSize: '0.875rem',
              color: '#6b7280'
            }}>
              ▼
            </span>
          </div>
          {isResultsExpanded && (
            <div className="table-container" style={{ paddingTop: 0 }}>
              <div className="activity-table">
                <div style={{ padding: '1.5rem' }}>
                  <div style={{
                    backgroundColor: '#f8f9fa',
                    border: '1px solid #e5e7eb',
                    borderRadius: '0.5rem',
                    padding: '1rem',
                    fontFamily: 'monospace',
                    fontSize: '0.875rem',
                    overflow: 'auto'
                  }}>
                    <pre style={{
                      margin: 0,
                      whiteSpace: 'pre-wrap',
                      wordWrap: 'break-word',
                      wordBreak: 'break-all',
                      overflowWrap: 'break-word'
                    }}>
                      {JSON.stringify(execution.results, null, 2)}
                    </pre>
                  </div>
                </div>
              </div>
            </div>
          )}
        </>
      )}

      {/* Execution Logs */}
      <h2 className="section-title">Körningsloggar</h2>
      {execution.logs.length === 0 ? (
        <div className="empty-state-container">
          <div className="empty-state">
            <div className="empty-state-icon">📝</div>
            <p className="empty-state-title">Inga loggar tillgängliga</p>
            <p className="empty-state-subtitle">Loggar visas här när skriptet körs</p>
          </div>
        </div>
      ) : (
        <div className="table-container">
          <div className="activity-table">
            <table className="table">
              <thead>
                <tr>
                  <th>Nivå</th>
                  <th>Tid</th>
                  <th>Steg</th>
                  <th>Meddelande</th>
                  <th>Data</th>
                </tr>
              </thead>
              <tbody>
                {execution.logs.map((log, index) => {
                  const styles = getLogLevelStyles(log.level);
                  return (
                    <tr key={index}>
                      <td>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                          <span>{styles.icon}</span>
                          <span className={`log-badge ${styles.badge}`}>
                            {log.level.toUpperCase()}
                          </span>
                        </div>
                      </td>
                      <td className="secondary-text" style={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
                        {new Date(log.timestamp).toLocaleTimeString('sv-SE', {
                          hour12: false,
                          hour: '2-digit',
                          minute: '2-digit',
                          second: '2-digit'
                        })}.{String(new Date(log.timestamp).getMilliseconds()).padStart(3, '0')}
                      </td>
                      <td>
                        {log.stepId ? (
                          <span className="step-badge">
                            {log.stepId.slice(0, 8)}
                          </span>
                        ) : (
                          <span className="secondary-text">—</span>
                        )}
                      </td>
                      <td style={{ lineHeight: '1.5' }}>
                        {log.message}
                      </td>
                      <td>
                        {log.data ? (
                          <details className="data-popup-container">
                            <summary className="data-link">
                              Visa data
                            </summary>
                            <div className="data-popup">
                              <pre>
                                {JSON.stringify(log.data, null, 2)}
                              </pre>
                            </div>
                          </details>
                        ) : (
                          <span className="secondary-text">—</span>
                        )}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  )
}
