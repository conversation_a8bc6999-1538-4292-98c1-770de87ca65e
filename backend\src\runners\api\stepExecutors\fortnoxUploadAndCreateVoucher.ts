import axios from 'axios';
import FormData from 'form-data';
import type { FortnoxUploadAndCreateVoucherStep } from '@rpa-project/shared/dist/esm/types/steps/api';
import { getDefaultVariableName } from '@rpa-project/shared';
import { StepExecutionResult } from '../../base';
import { customerService } from '../../../services/customerService';
import { LLMService } from '../../../services/llm/LLMService';
import { LLMProviderFactory } from '../../../services/llm/LLMProviderFactory';

/**
 * Executor context for Fortnox steps
 */
export interface FortnoxExecutorContext {
  variables: Record<string, any>;
  onLog: (log: { level: 'info' | 'warn' | 'error'; message: string; stepId?: string }) => void;
  interpolateVariables: (text: string, variables: Record<string, any>) => string;
  customerId?: string;
  // openai?: OpenAI; // TA BORT denna rad
}

/**
 * Fortnox Archive API response interface
 * Note: The actual response structure may vary, so we need to handle multiple formats
 */
interface FortnoxArchiveResponse {
  Archive?: {
    Id?: string;
    Name?: string;
    Size?: number;
    Path?: string;
  };
  File?: {
    Id?: string;
    Name?: string;
    Size?: number;
    Path?: string;
    ArchiveFileId?: string;
    '@url'?: string;
    Comments?: string | null;
  };
  // Alternative response format (if API returns different structure)
  Id?: string;
  Name?: string;
  Size?: number;
  Path?: string;
  // Allow any additional properties for flexible response handling
  [key: string]: any;
}

/**
 * Fortnox Voucher Row interface
 */
interface FortnoxVoucherRow {
  Account: string;
  Debit?: number;
  Credit?: number;
  Description?: string;
}

/**
 * Fortnox Voucher interface
 */
interface FortnoxVoucher {
  Description: string;
  TransactionDate: string;
  VoucherSeries: string;
  VoucherRows: FortnoxVoucherRow[];
}

/**
 * AI response interface for voucher rows
 */
interface AIVoucherRowsResponse {
  rows: Array<{
    account: string;
    debit?: number;
    credit?: number;
    description?: string;
  }>;
  transactionDate?: string;
  explanation?: string;
}

/**
 * Execute Fortnox Upload and Create Voucher step
 */
export async function executeFortnoxUploadAndCreateVoucher(
  step: FortnoxUploadAndCreateVoucherStep,
  context: FortnoxExecutorContext,
  stepIndex?: number
): Promise<StepExecutionResult> {
  const { variables, onLog, interpolateVariables, customerId } = context;

  try {
    onLog({
      level: 'info',
      message: `Executing Fortnox Upload and Create Voucher: ${step.id}`,
      stepId: step.id
    });

    // Get Fortnox token for the customer
    if (!customerId) {
      throw new Error('Customer ID is required for Fortnox API calls');
    }

    const fortnoxTokens = await customerService.getCustomerTokensWithData(customerId);
    const fortnoxToken = fortnoxTokens.find(token => token.provider === 'Fortnox' && token.apiToken);

    if (!fortnoxToken || !fortnoxToken.apiToken) {
      throw new Error('No valid Fortnox token found for customer');
    }

    // STEP 1: Process voucher data with AI and validate balance
    onLog({
      level: 'info',
      message: 'Step 1: Processing voucher data with AI and validating balance',
      stepId: step.id
    });

    // Get voucher input data
    // Handle both direct variable names and ${variableName} syntax
    let actualVoucherVariableName = step.voucherInputVariable;

    // Check if voucherInputVariable contains ${...} syntax and extract the variable name
    const voucherVariableMatch = step.voucherInputVariable.match(/^\$\{([^}]+)\}$/);
    if (voucherVariableMatch) {
      actualVoucherVariableName = voucherVariableMatch[1];
    }

    const inputData = variables[actualVoucherVariableName];

    if (!inputData) {
      onLog({
        level: 'error',
        message: `Available variables: ${Object.keys(variables).join(', ')}`,
        stepId: step.id
      });
      throw new Error(`No input data found in variable: ${actualVoucherVariableName}`);
    }

    // Create system prompt for AI processing
    const systemPrompt = `Du är en expert på bokföring och Fortnox API. Din uppgift är att analysera den givna datan och skapa korrekta verifikationsrader.

Svara ENDAST med ett JSON-objekt i följande format:
{
  "rows": [
    {
      "account": "kontonummer (endast siffror)",
      "debit": belopp (om debet),
      "credit": belopp (om kredit),
      "description": "beskrivning av raden"
    }
  ],
  "transactionDate": "YYYY-MM-DD",
  "explanation": "kort förklaring av verifikationen"
}

VIKTIGA REGLER:
- Varje rad ska ha ANTINGEN debit ELLER credit, aldrig båda
- Summan av alla debet-belopp MÅSTE vara exakt lika med summan av alla kredit-belopp
- Kontonummer ska vara numeriska (endast siffror)
- Använd svenska kontoplan
- Transaktionsdatum ska vara i YYYY-MM-DD format

INSTRUKTIONER:
Användaren kommer att ange vilka konton som ska användas i sin prompt. Följ användarens instruktioner för kontohantering.`;

    // Interpolate the AI prompt with variables
    const interpolatedPrompt = interpolateVariables(step.aiPrompt, variables);

    // Prepare input data as string
    const inputDataStr = typeof inputData === 'string' ? inputData : JSON.stringify(inputData, null, 2);

    // Get provider and model info for logging
    const provider = LLMProviderFactory.getInstance();
    const defaultModel = LLMProviderFactory.getDefaultModel();

    onLog({
      level: 'info',
      message: `Sending voucher data to ${provider.name} (${defaultModel}) for processing...`,
      stepId: step.id
    });

    // Process with AI
    let aiResponse: string;
    try {
      const completion = await LLMService.createChatCompletion([
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: `${interpolatedPrompt}\n\nData att analysera:\n${inputDataStr}`
        }
      ], {
        temperature: 0.1
      });

      aiResponse = completion.content;
    } catch (aiError) {
      onLog({
        level: 'error',
        message: `AI processing failed: ${aiError instanceof Error ? aiError.message : 'Unknown error'}`,
        stepId: step.id
      });
      throw new Error(`AI processing failed: ${aiError instanceof Error ? aiError.message : 'Unknown error'}`);
    }

    // Parse AI response
    let aiVoucherData: AIVoucherRowsResponse;
    try {
      // Clean the response to extract JSON
      const cleanedResponse = aiResponse.replace(/```json\s*|\s*```/g, '').trim();
      aiVoucherData = JSON.parse(cleanedResponse);
    } catch (parseError) {
      onLog({
        level: 'error',
        message: `Failed to parse AI response as JSON: ${parseError instanceof Error ? parseError.message : 'Unknown error'}. Response: ${aiResponse}`,
        stepId: step.id
      });
      throw new Error(`Failed to parse AI response as JSON: ${parseError instanceof Error ? parseError.message : 'Unknown error'}`);
    }

    // Validate AI response structure
    if (!aiVoucherData.rows || !Array.isArray(aiVoucherData.rows) || aiVoucherData.rows.length === 0) {
      throw new Error('AI response must contain a non-empty "rows" array');
    }

    // Validate and calculate balance
    let totalDebit = 0;
    let totalCredit = 0;
    const voucherRows: FortnoxVoucherRow[] = [];

    onLog({
      level: 'info',
      message: `AI generated ${aiVoucherData.rows.length} voucher rows, validating balance...`,
      stepId: step.id
    });

    for (const aiRow of aiVoucherData.rows) {
      // Validate amounts
      if (aiRow.debit && aiRow.credit) {
        throw new Error(`Row for account ${aiRow.account} cannot have both debit and credit`);
      }

      if (!aiRow.debit && !aiRow.credit) {
        throw new Error(`Row for account ${aiRow.account} must have either debit or credit`);
      }

      // Validate account number format (basic validation)
      if (!aiRow.account || !/^\d+$/.test(aiRow.account)) {
        throw new Error(`Invalid account number format: ${aiRow.account}. Must be numeric.`);
      }

      const voucherRow: FortnoxVoucherRow = {
        Account: aiRow.account,
        Description: aiRow.description || interpolateVariables(step.voucherDescription || '', variables)
      };

      if (aiRow.debit) {
        voucherRow.Debit = aiRow.debit;
        totalDebit += aiRow.debit;
      }

      if (aiRow.credit) {
        voucherRow.Credit = aiRow.credit;
        totalCredit += aiRow.credit;
      }

      voucherRows.push(voucherRow);
    }

    // Validate voucher balance (critical validation before any API calls)
    if (Math.abs(totalDebit - totalCredit) > 0.01) {
      onLog({
        level: 'error',
        message: `Voucher is not balanced! Debit: ${totalDebit}, Credit: ${totalCredit}, Difference: ${Math.abs(totalDebit - totalCredit)}`,
        stepId: step.id
      });
      throw new Error(`Voucher is not balanced. Debit: ${totalDebit}, Credit: ${totalCredit}. Difference: ${Math.abs(totalDebit - totalCredit)}`);
    }

    onLog({
      level: 'info',
      message: `Voucher balance validated successfully: Debit=${totalDebit}, Credit=${totalCredit}`,
      stepId: step.id
    });

    // Determine transaction date
    let transactionDate = aiVoucherData.transactionDate || step.transactionDate || new Date().toISOString().split('T')[0];
    transactionDate = interpolateVariables(transactionDate, variables);

    // STEP 2: Upload file to Fortnox Archive
    onLog({
      level: 'info',
      message: 'Step 2: Uploading file to Fortnox Archive',
      stepId: step.id
    });

    // Get base64 file content from variable
    // Handle both direct variable names and ${variableName} syntax
    let actualFileVariableName = step.fileInputVariable;

    // Check if fileInputVariable contains ${...} syntax and extract the variable name
    const fileVariableMatch = step.fileInputVariable.match(/^\$\{([^}]+)\}$/);
    if (fileVariableMatch) {
      actualFileVariableName = fileVariableMatch[1];
    }

    const base64Content = variables[actualFileVariableName];

    if (!base64Content) {
      onLog({
        level: 'error',
        message: `Available variables: ${Object.keys(variables).join(', ')}`,
        stepId: step.id
      });
      throw new Error(`No file content found in variable: ${actualFileVariableName}`);
    }

    // Extract base64 data (remove data URL prefix if present)
    let base64Data = base64Content;
    if (typeof base64Data === 'string' && base64Data.includes(',')) {
      base64Data = base64Data.split(',')[1];
    }

    // Convert base64 to buffer
    let fileBuffer: Buffer;
    try {
      fileBuffer = Buffer.from(base64Data, 'base64');
    } catch (bufferError) {
      throw new Error(`Invalid base64 data in variable ${actualFileVariableName}: ${bufferError instanceof Error ? bufferError.message : 'Unknown error'}`);
    }

    // Determine filename
    let filename: string = step.filename || '';
    if (!filename) {
      // Try to get filename from related variable
      const filenameVariable = `${actualFileVariableName}_filename`;
      filename = variables[filenameVariable] || 'uploaded_file.bin';
    }
    filename = interpolateVariables(filename, variables);

    // Create FormData for multipart upload
    const formData = new FormData();
    formData.append('file', fileBuffer, {
      filename: filename,
      contentType: 'application/octet-stream'
    });

    // Add file description if provided
    if (step.fileDescription) {
      const interpolatedFileDescription = interpolateVariables(step.fileDescription, variables);
      formData.append('description', interpolatedFileDescription);
    }

    // Upload file to Fortnox Archive
    let uploadResponse;
    try {
      uploadResponse = await axios.post(
        'https://api.fortnox.se/3/archive',
        formData,
        {
          headers: {
            'Authorization': `Bearer ${fortnoxToken.apiToken}`,
            'Accept': 'application/json',
            ...formData.getHeaders()
          },
          maxContentLength: Infinity,
          maxBodyLength: Infinity
        }
      );
    } catch (apiError: any) {
      const errorDetails = apiError.response?.data || apiError.message;
      const statusCode = apiError.response?.status;

      onLog({
        level: 'error',
        message: `Fortnox Archive API error (${statusCode}): ${JSON.stringify(errorDetails, null, 2)}`,
        stepId: step.id
      });

      throw new Error(`Fortnox Archive API error (${statusCode}): ${JSON.stringify(errorDetails)}`);
    }

    // Handle both legacy and new response formats
    const uploadedFile = uploadResponse.data;
    let fileId: string;
    let fileName: string;

    // Try new format first (File object)
    if (uploadedFile.File && uploadedFile.File.Id) {
      fileId = uploadedFile.File.Id;
      fileName = uploadedFile.File.Name;
      onLog({
        level: 'info',
        message: `Using new API response format - File ID: ${fileId}`,
        stepId: step.id
      });
    }
    // Fall back to legacy format (Archive object)
    else if (uploadedFile.Archive && uploadedFile.Archive.Id) {
      fileId = uploadedFile.Archive.Id;
      fileName = uploadedFile.Archive.Name;
      onLog({
        level: 'info',
        message: `Using legacy API response format - Archive ID: ${fileId}`,
        stepId: step.id
      });
    }
    // Handle direct response format
    else if (uploadedFile.Id) {
      fileId = uploadedFile.Id;
      fileName = uploadedFile.Name || filename;
      onLog({
        level: 'info',
        message: `Using direct response format - ID: ${fileId}`,
        stepId: step.id
      });
    }
    // Handle ArchiveFileId format
    else if (uploadedFile.File && uploadedFile.File.ArchiveFileId) {
      fileId = uploadedFile.File.ArchiveFileId;
      fileName = uploadedFile.File.Name;
      onLog({
        level: 'info',
        message: `Using ArchiveFileId format - File ID: ${fileId}`,
        stepId: step.id
      });
    }
    else {
      onLog({
        level: 'error',
        message: `Unexpected Fortnox Archive response format: ${JSON.stringify(uploadResponse.data, null, 2)}`,
        stepId: step.id
      });
      throw new Error(`Unexpected Fortnox Archive response format: ${JSON.stringify(uploadResponse.data)}`);
    }

    if (!fileId) {
      onLog({
        level: 'error',
        message: `Missing file ID in Fortnox Archive response: ${JSON.stringify(uploadResponse.data, null, 2)}`,
        stepId: step.id
      });
      throw new Error(`Missing file ID in Fortnox Archive response: ${JSON.stringify(uploadResponse.data)}`);
    }

    onLog({
      level: 'info',
      message: `File uploaded successfully: ${fileName} (ID: ${fileId})`,
      stepId: step.id
    });

    // STEP 3: Create voucher with the validated AI data
    onLog({
      level: 'info',
      message: 'Step 3: Creating voucher with validated AI data',
      stepId: step.id
    });

    // Create voucher
    const voucher: FortnoxVoucher = {
      Description: interpolateVariables(step.voucherDescription || 'AI Generated Voucher with File', variables),
      TransactionDate: transactionDate,
      VoucherSeries: step.voucherSeries || 'A',
      VoucherRows: voucherRows
    };

    onLog({
        level: 'info',
        message: `Voucher data ${voucher}`,
        stepId: step.id,
        data: { [variableName]: parsedJson }
      });

    // Send voucher to Fortnox
    let voucherResponse;
    try {
      voucherResponse = await axios.post(
        'https://api.fortnox.se/3/vouchers',
        { Voucher: voucher },
        {
          headers: {
            'Authorization': `Bearer ${fortnoxToken.apiToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
    } catch (apiError: any) {
      const errorDetails = apiError.response?.data || apiError.message;
      const statusCode = apiError.response?.status;

      onLog({
        level: 'error',
        message: `Fortnox Voucher API error (${statusCode}): ${JSON.stringify(errorDetails, null, 2)}`,
        stepId: step.id
      });

      throw new Error(`Fortnox Voucher API error (${statusCode}): ${JSON.stringify(errorDetails)}`);
    }

    const createdVoucher = voucherResponse.data.Voucher;

    onLog({
      level: 'info',
      message: `Voucher created successfully: ${createdVoucher.VoucherSeries}${createdVoucher.VoucherNumber}`,
      stepId: step.id
    });

    // STEP 4: Attach file to voucher
    onLog({
      level: 'info',
      message: 'Step 4: Attaching file to voucher',
      stepId: step.id
    });

    try {
      const voucherFileConnection = {
        FileId: fileId,
        VoucherNumber: createdVoucher.VoucherNumber,
        VoucherSeries: createdVoucher.VoucherSeries
      };

      await axios.post(
        'https://api.fortnox.se/3/voucherfileconnections',
        { VoucherFileConnection: voucherFileConnection },
        {
          headers: {
            'Authorization': `Bearer ${fortnoxToken.apiToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );

      onLog({
        level: 'info',
        message: `File ${fileId} attached to voucher successfully`,
        stepId: step.id
      });

    } catch (attachError: any) {
      const errorDetails = attachError.response?.data || attachError.message;
      const statusCode = attachError.response?.status || 'unknown';

      onLog({
        level: 'error',
        message: `Failed to attach file to voucher (${statusCode}): ${JSON.stringify(errorDetails, null, 2)}`,
        stepId: step.id
      });

      throw new Error(`Failed to attach file to voucher (${statusCode}): ${JSON.stringify(errorDetails)}`);
    }

    // Store combined result in variables
    const variableName = step.variableName || getDefaultVariableName('fortnoxUploadAndCreateVoucher', stepIndex);
    const combinedResult = {
      // File information
      fileId: fileId,
      filename: fileName,
      fileSize: uploadedFile.Archive?.Size || uploadedFile.File?.Size || uploadedFile.Size || 0,
      filePath: uploadedFile.Archive?.Path || uploadedFile.File?.Path || uploadedFile.Path || '',
      // Voucher information
      voucherNumber: createdVoucher.VoucherNumber,
      voucherSeries: createdVoucher.VoucherSeries,
      voucherId: createdVoucher.VoucherNumber,
      totalAmount: voucher.VoucherRows.reduce((sum, row) => sum + (row.Debit || 0), 0),
      rowsCount: voucher.VoucherRows.length,
      aiExplanation: "Verifikation skapad med validerad AI-data",
      // Combined information
      attachedFileId: fileId,
      fullVoucherResponse: createdVoucher,
      fullFileResponse: uploadedFile.Archive || uploadedFile.File || uploadedFile
    };

    variables[variableName] = combinedResult;

    onLog({
      level: 'info',
      message: `Combined operation completed successfully: File uploaded and voucher ${createdVoucher.VoucherSeries}${createdVoucher.VoucherNumber} created with attachment`,
      stepId: step.id
    });

    return {
      success: true,
      variables: {
        [variableName]: combinedResult
      }
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    onLog({
      level: 'error',
      message: `Error in combined upload and voucher creation: ${errorMessage}`,
      stepId: step.id
    });

    return {
      success: false,
      error: errorMessage
    };
  }
}
